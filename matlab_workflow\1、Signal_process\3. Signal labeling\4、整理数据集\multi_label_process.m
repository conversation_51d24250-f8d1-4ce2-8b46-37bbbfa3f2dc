%% MULTI_LABEL_PROCESS 多标注文件批量处理程序
%   批量处理当前目录下所有label开头的标注文件，提取对应的信号数据片段并统一保存。
%   主要用于肠鸣音信号分析项目中的数据集构建和管理。
%
%   Description:
%   该脚本自动扫描当前目录下所有以'label'开头的.mat标注文件，
%   从每个标注文件中提取标签信息和对应的原始信号文件路径，
%   根据ROILimits时间范围从原始信号中提取相应的数据片段，
%   并按照统一的命名规则保存到输出文件夹中。
%
%   Input Files:
%   - label*.mat: 标注文件，包含ls结构体
%     - ls.Labels: 包含文件路径和标注信息的表格
%     - ROILimits: 时间范围 [开始时间, 结束时间] (秒)
%     - Value: 标签值 (如SB, MB, CRS等)
%
%   Output Files:
%   - output_all_labels/: 输出文件夹
%   - 文件命名格式: "标注文件名_原文件名_标签值_序号.mat"
%   - 每个文件包含extractedData变量（提取的信号片段）
%
%   Processing Steps:
%   1. 扫描当前目录下所有label*.mat文件
%   2. 创建输出目录output_all_labels
%   3. 逐个处理每个标注文件：
%      a. 加载标注文件中的ls结构体
%      b. 提取文件路径列表和标注信息表
%      c. 遍历每个原始信号文件
%      d. 根据ROILimits从原始文件中提取tt1变量的对应时间段
%      e. 保存提取的数据片段到输出文件夹
%   4. 显示处理进度和统计信息
%
%   Requirements:
%   - 标注文件必须包含正确格式的ls结构体
%   - 原始信号文件路径必须有效且包含tt1变量
%   - tt1变量必须是时间表(timetable)格式
%
%   Example Usage:
%   1. 将所有label*.mat标注文件放在当前目录
%   2. 确保原始信号文件路径正确
%   3. 运行此脚本: multi_label_process
%   4. 检查output_all_labels文件夹中的输出结果
%
%   Notes:
%   - 脚本会自动创建输出目录
%   - 处理过程中会显示详细的进度信息
%   - 如果遇到无效的标注表，会发出警告并跳过
%   - 建议在处理前备份原始数据
%
%   See also: DIR, LOAD, SAVE, TIMETABLE, TIMERANGE

clear all;
clc;
close all;

% 获取当前目录下所有 label 开头的 .mat 文件
labelFiles = dir('label*.mat');
totalLabelFiles = length(labelFiles);

% 创建输出目录
outputDir = fullfile(pwd, 'output_all_labels');
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
end

% 初始化总提取计数器
totalExtractedFiles = 0;

% 打印总体进度开始
fprintf('开始处理 %d 个 label 文件...\n', totalLabelFiles);

% 遍历所有 label 文件
for fileIndex = 1:totalLabelFiles
    % 打印当前处理的文件信息
    fprintf('\n正在处理第 %d/%d 个文件: %s\n', ...
        fileIndex, totalLabelFiles, labelFiles(fileIndex).name);
    
    % 加载当前 label 文件
    currentLabelFile = fullfile(labelFiles(fileIndex).folder, labelFiles(fileIndex).name);
    load(currentLabelFile, 'ls');
    
    % 获取 Labels
    labels = ls.Labels;
    
    % 提取文件路径列和 BS 表
    filePaths = labels.Row;
    bsTables = labels{:, 1};
    
    % 遍历每一行，提取并处理 bsTables
    for i = 1:numel(bsTables)
        % 打印当前处理的文件路径
        fprintf('  处理文件路径: %s (%d/%d)\n', filePaths{i}, i, numel(bsTables));
        
        % 当前文件路径
        currentFilePath = filePaths{i};
        
        % 当前的 BS 表
        currentBsTable = bsTables{i};
        
        % 提取文件名，不含路径和扩展名
        [~, fileName, ~] = fileparts(currentFilePath);
        
        % 验证当前 BS 表是否为 table
        if istable(currentBsTable)
            % 遍历当前 BS 表的每一行
            for j = 1:height(currentBsTable)
                % 打印提取进度
                fprintf('    提取第 %d/%d 个数据段\n', j, height(currentBsTable));
                
                % 提取当前行的 ROIlimits 和 Value
                roiLimits = currentBsTable.ROILimits(j, :); % 起始时间和结束时间
                labelValue = currentBsTable.Value(j); % 标签值（从元胞数组提取）
                
                % 加载对应的信号数据文件，提取 tt1
                load(currentFilePath, 'tt1'); % 只加载 tt1
                
                % 根据 ROIlimits 提取时间范围内的数据
                startTime = roiLimits(1); % 起始时间
                endTime = roiLimits(2); % 结束时间
                extractedData = tt1(timerange(seconds(startTime), seconds(endTime)), :);
                
                % 构造新的文件名（包含原 label 文件名以区分）
                newFileName = sprintf('%s_%s_%s_%d.mat', ...
                    labelFiles(fileIndex).name(1:end-4), fileName, labelValue, j);
                
                % 保存文件到输出文件夹
                save(fullfile(outputDir, newFileName), 'extractedData');
                
                % 增加总提取文件计数
                totalExtractedFiles = totalExtractedFiles + 1;
            end
        else
            warning('第 %d 行的 BS 表不是有效的 table，跳过处理。', i);
        end
    end
end

% 打印总体完成信息
fprintf('\n所有 label 文件处理完成！\n');
fprintf('总计处理 %d 个 label 文件\n', totalLabelFiles);
fprintf('总计提取 %d 个数据段\n', totalExtractedFiles);
fprintf('提取的数据已保存到 %s 文件夹\n', outputDir);















