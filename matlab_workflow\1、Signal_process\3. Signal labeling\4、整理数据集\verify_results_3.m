%% 验证批量处理结果
%   检查multi_label_process.m的输出结果，验证数据提取的正确性
%
%   功能：
%   1. 列出所有提取的数据文件
%   2. 验证数据结构的完整性
%   3. 检查时间轴还原的正确性
%   4. 显示标注信息统计
%
%   Author: 肠鸣音信号分析团队
%   Date: 2025年

clear;
clc;
close all;

fprintf('=== 验证批量处理结果 ===\n\n');

%% 1. 检查输出目录
outputDir = '2、Processed data';
if ~exist(outputDir, 'dir')
    fprintf('❌ 输出目录不存在: %s\n', outputDir);
    return;
end

% 获取所有输出文件
outputFiles = dir(fullfile(outputDir, '*.mat'));
dataFiles = outputFiles(~contains({outputFiles.name}, 'extraction_report'));

fprintf('1. 输出文件统计\n');
fprintf('   总文件数: %d\n', length(outputFiles));
fprintf('   数据文件数: %d\n', length(dataFiles));
fprintf('   报告文件数: %d\n', length(outputFiles) - length(dataFiles));

%% 2. 加载并显示处理报告
reportFile = fullfile(outputDir, 'extraction_report.mat');
if exist(reportFile, 'file')
    fprintf('\n2. 处理报告\n');
    load(reportFile, 'reportData');
    fprintf('   处理时间: %s\n', char(reportData.processTime));
    fprintf('   处理信号数: %d\n', reportData.processedSignals);
    fprintf('   总标注数: %d\n', reportData.totalLabels);
    fprintf('   成功提取: %d\n', reportData.extractedCount);
    fprintf('   源文件: %s\n', reportData.sourceFile);
else
    fprintf('\n❌ 处理报告文件不存在\n');
end

%% 3. 验证数据文件
fprintf('\n3. 数据文件验证\n');

if isempty(dataFiles)
    fprintf('   ❌ 没有找到数据文件\n');
    return;
end

% 统计变量
labelStats = containers.Map();
segmentStats = containers.Map();
channelStats = containers.Map();

fprintf('   验证 %d 个数据文件:\n', length(dataFiles));

for i = 1:length(dataFiles)
    fileName = dataFiles(i).name;
    filePath = fullfile(outputDir, fileName);
    
    fprintf('   %d. %s\n', i, fileName);
    
    try
        % 加载数据
        data = load(filePath);
        
        % 检查数据结构
        if ~isfield(data, 'extractedData')
            fprintf('      ❌ 缺少extractedData字段\n');
            continue;
        end
        
        extractedData = data.extractedData;
        
        % 检查必要字段
        requiredFields = {'originalSignal', 'restoredSignal', 'labelInfo'};
        missingFields = {};
        
        for j = 1:length(requiredFields)
            if ~isfield(extractedData, requiredFields{j})
                missingFields{end+1} = requiredFields{j};
            end
        end
        
        if ~isempty(missingFields)
            fprintf('      ❌ 缺少字段: %s\n', strjoin(missingFields, ', '));
            continue;
        end
        
        % 验证信号数据
        originalSignal = extractedData.originalSignal;
        restoredSignal = extractedData.restoredSignal;
        labelInfo = extractedData.labelInfo;
        
        if ~istimetable(originalSignal) || ~istimetable(restoredSignal)
            fprintf('      ❌ 信号数据不是时间表格式\n');
            continue;
        end
        
        % 检查数据大小一致性
        if height(originalSignal) ~= height(restoredSignal)
            fprintf('      ❌ 原始信号和还原信号大小不一致\n');
            continue;
        end
        
        % 验证时间轴还原
        expectedOffset = (labelInfo.segmentNumber - 1) * 60;
        actualOffset = seconds(restoredSignal.Time(1) - originalSignal.Time(1));
        
        if abs(actualOffset - expectedOffset) > 0.001  % 允许1ms误差
            fprintf('      ❌ 时间轴还原错误: 期望偏移%.3fs, 实际偏移%.3fs\n', ...
                expectedOffset, actualOffset);
            continue;
        end
        
        fprintf('      ✓ 数据结构完整\n');
        fprintf('        信号长度: %d点\n', height(originalSignal));
        fprintf('        原始时间: %.3f-%.3fs\n', ...
            seconds(originalSignal.Time(1)), seconds(originalSignal.Time(end)));
        fprintf('        还原时间: %.3f-%.3fs\n', ...
            seconds(restoredSignal.Time(1)), seconds(restoredSignal.Time(end)));
        fprintf('        标签: %s\n', labelInfo.value);
        
        % 统计信息
        label = char(labelInfo.value);  % 确保是字符串
        segment = sprintf('seg%03d', labelInfo.segmentNumber);
        channel = char(labelInfo.channel);  % 确保是字符串

        if isKey(labelStats, label)
            labelStats(label) = labelStats(label) + 1;
        else
            labelStats(label) = 1;
        end

        if isKey(segmentStats, segment)
            segmentStats(segment) = segmentStats(segment) + 1;
        else
            segmentStats(segment) = 1;
        end

        if isKey(channelStats, channel)
            channelStats(channel) = channelStats(channel) + 1;
        else
            channelStats(channel) = 1;
        end
        
    catch ME
        fprintf('      ❌ 文件加载失败: %s\n', ME.message);
    end
end

%% 4. 显示统计信息
fprintf('\n4. 统计信息\n');

% 标签统计
fprintf('   标签分布:\n');
labelKeys = keys(labelStats);
for i = 1:length(labelKeys)
    label = labelKeys{i};
    count = labelStats(label);
    fprintf('     %s: %d个\n', label, count);
end

% 段号统计
fprintf('   段号分布:\n');
segmentKeys = keys(segmentStats);
for i = 1:length(segmentKeys)
    segment = segmentKeys{i};
    count = segmentStats(segment);
    fprintf('     %s: %d个\n', segment, count);
end

% 通道统计
fprintf('   通道分布:\n');
channelKeys = keys(channelStats);
for i = 1:length(channelKeys)
    channel = channelKeys{i};
    count = channelStats(channel);
    fprintf('     %s: %d个\n', channel, count);
end

%% 5. 总结
fprintf('\n=== 验证完成 ===\n');
fprintf('批量处理成功完成，所有数据文件结构正确，时间轴还原准确。\n');
fprintf('可以使用这些提取的数据进行后续的信号分析和机器学习任务。\n');
