# multi_label_process.m 修改报告

## 修改概述

本报告详细说明了对 `multi_label_process.m` 脚本的修改，使其适配当前文件夹的数据结构和标注文件格式。

## 原始问题分析

### 1. 文件夹结构不匹配
- **原始脚本**: 扫描当前目录下所有 `label*.mat` 文件
- **实际结构**: 标注文件位于 `4、Label/ls_data3.mat`

### 2. 标注文件格式差异
- **原始脚本**: 假设多个独立的标注文件
- **实际格式**: 单个 `labeledSignalSet` 对象，包含所有标注信息

### 3. 数据访问方式不同
- **原始脚本**: 直接从标注文件中的文件路径加载数据
- **实际需求**: 需要解析信号名称，构造对应的原始数据文件路径

### 4. 输出组织方式
- **原始脚本**: 简单的文件名拼接
- **改进需求**: 需要时间轴还原和更完整的数据结构

## 主要修改内容

### 1. 文件路径配置
```matlab
% 修改前：扫描当前目录
labelFiles = dir('label*.mat');

% 修改后：指定具体路径
labelFile = fullfile('4、Label', 'ls_data3.mat');
rawDataDir = '1、Raw data';
outputDir = '2、Processed data';
```

### 2. 标注文件处理
```matlab
% 修改前：遍历多个标注文件
for fileIndex = 1:totalLabelFiles
    load(currentLabelFile, 'ls');
    
% 修改后：处理单个labeledSignalSet对象
load(labelFile, 'ls');
labels = ls.Labels;
for i = 1:height(labels)
    signalName = labels.Row{i};
    bsTable = labels{i, 'BS'}{1};
```

### 3. 原始数据文件访问
```matlab
% 修改前：直接使用标注文件中的路径
load(currentFilePath, 'tt1');

% 修改后：解析信号名称并构造文件路径
[datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);
rawFileName = sprintf('%s_seg%03d_tt.mat', datasetInfo, segmentNum);
rawFilePath = fullfile(rawDataDir, rawFileName);
rawData = load(rawFilePath);
signalData = rawData.tt1;
```

### 4. 时间轴还原功能
```matlab
% 新增：计算还原后的真实时间
segmentOffset = (segmentNum - 1) * 60; % 每段60秒
realStartTime = roiLimits(1) + segmentOffset;
realEndTime = roiLimits(2) + segmentOffset;

% 新增：创建还原时间的时间表
originalTime = extractedSignal.Time;
restoredTime = originalTime + seconds(segmentOffset);
restoredSignal = extractedSignal;
restoredSignal.Time = restoredTime;
```

### 5. 输出数据结构增强
```matlab
% 修改前：简单保存提取的信号
save(fullfile(outputDir, newFileName), 'extractedData');

% 修改后：完整的数据结构
extractedData = struct();
extractedData.originalSignal = extractedSignal;      % 原始时间的信号
extractedData.restoredSignal = restoredSignal;       % 还原时间的信号
extractedData.labelInfo = struct();
extractedData.labelInfo.value = labelValue;
extractedData.labelInfo.originalTimeRange = roiLimits;
extractedData.labelInfo.restoredTimeRange = [realStartTime, realEndTime];
extractedData.labelInfo.sourceFile = signalName;
extractedData.labelInfo.segmentNumber = segmentNum;
extractedData.labelInfo.channel = channelInfo;
```

### 6. 错误处理和验证
```matlab
% 新增：文件存在性检查
if ~exist(labelFile, 'file')
    error('标注文件不存在: %s', labelFile);
end

% 新增：对象类型验证
if ~isa(ls, 'labeledSignalSet')
    error('ls变量不是labeledSignalSet对象，而是: %s', class(ls));
end

% 新增：原始数据文件检查
if ~exist(rawFilePath, 'file')
    warning('原始数据文件不存在: %s', rawFilePath);
    continue;
end
```

## 新增功能

### 1. parseSignalName 函数
- 解析标准化的信号文件名
- 提取数据集标识、段号和通道信息
- 支持正则表达式模式匹配

### 2. 处理报告生成
- 详细的处理统计信息
- 自动保存处理报告到输出目录
- 包含时间戳和处理参数

### 3. 进度显示优化
- 更详细的处理进度信息
- 包含时间轴还原的显示
- 错误和警告的清晰提示

## 文件命名规则

### 修改前
```
{标注文件名}_{原文件名}_{标签值}_{序号}.mat
```

### 修改后
```
{dataset}_seg{XXX}_{channel}_{label}_{index}.mat
```

例如：`data3_seg002_tt1_MB_001.mat`

## 时间轴还原算法

### 原理
每个分段文件包含60秒的数据，时间轴从0开始。为了还原到连续时间轴：

```
还原时间 = 原始时间 + (段号 - 1) × 60秒
```

### 示例
- seg001: [0,60]秒 → [0,60]秒（无偏移）
- seg002: [0,60]秒 → [60,120]秒（偏移60秒）
- seg003: [0,60]秒 → [120,180]秒（偏移120秒）

## 使用说明

### 1. 运行前检查
```matlab
% 运行测试脚本验证环境
test_multi_label_process
```

### 2. 执行批量处理
```matlab
% 运行主处理脚本
multi_label_process
```

### 3. 检查结果
- 查看 `2、Processed data` 文件夹中的输出文件
- 检查 `extraction_report.mat` 处理报告

## 兼容性说明

### 支持的数据格式
- labeledSignalSet 对象（MATLAB Signal Processing Toolbox）
- 时间表（timetable）格式的信号数据
- 标准化的文件命名规范

### 系统要求
- MATLAB R2018b 或更高版本
- Signal Processing Toolbox
- 足够的磁盘空间用于存储提取的数据

## 测试验证

创建了 `test_multi_label_process.m` 脚本用于验证：
1. 文件夹结构检查
2. 标注文件格式验证
3. 信号名称解析测试
4. 原始数据文件访问测试
5. 单个标注提取过程测试

## 性能优化

### 内存管理
- 避免重复加载相同的原始文件
- 及时释放不需要的变量
- 使用结构化的数据访问

### 错误恢复
- 单个文件处理失败不影响整体流程
- 详细的错误信息和警告提示
- 跳过无效数据继续处理

## 后续建议

1. **参数化配置**: 将硬编码的路径和参数提取为配置文件
2. **并行处理**: 对于大量数据，考虑使用并行计算
3. **数据验证**: 增加更多的数据完整性检查
4. **可视化**: 添加处理结果的可视化功能
5. **日志记录**: 实现更详细的日志记录系统

## 总结

修改后的脚本完全适配了当前文件夹的数据结构，增加了时间轴还原功能，提供了更完整的数据输出格式，并包含了全面的错误处理和验证机制。建议先运行测试脚本验证环境，然后执行批量处理。
