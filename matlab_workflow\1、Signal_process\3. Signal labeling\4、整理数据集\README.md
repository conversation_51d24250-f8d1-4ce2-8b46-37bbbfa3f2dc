# 整理数据集 - 使用指南

## 概述

本模块用于从标注文件中批量提取肠鸣音信号片段，支持时间轴还原和完整的数据结构输出。

## 文件夹结构

```
4、整理数据集/
├── 1、Raw data/              # 原始分段信号数据
│   ├── data1_5min_seg001_tt.mat
│   ├── data1_5min_seg002_tt.mat
│   └── ...
├── 2、Processed data/        # 输出：提取的信号片段
├── 3、Backup/               # 备份文件夹
├── 4、Label/                # 标注文件
│   └── ls_data3.mat         # labeledSignalSet对象
├── multi_label_process.m    # 主处理脚本
├── test_multi_label_process.m # 测试脚本
├── verify_results.m         # 结果验证脚本
├── check_label_file.m       # 标注文件检查脚本
└── README.md               # 本文件
```

## 快速开始

### 1. 环境检查
```matlab
% 运行测试脚本检查环境
test_multi_label_process
```

### 2. 批量处理
```matlab
% 运行主处理脚本
multi_label_process
```

### 3. 检查结果
```matlab
% 验证处理结果
verify_results
```
- 查看 `2、Processed data` 文件夹中的输出文件
- 检查 `extraction_report.mat` 处理报告

## 输出文件格式

### 文件命名
```
{dataset}_seg{XXX}_{channel}_{label}_{index}.mat
```
例如：`data3_seg002_tt1_MB_001.mat`

### 数据结构
每个输出文件包含 `extractedData` 结构体：
```matlab
extractedData.originalSignal    % 原始时间轴的信号数据
extractedData.restoredSignal    % 还原时间轴的信号数据
extractedData.labelInfo         % 标注信息
  .value                        % 标签值（如SB, MB, CRS）
  .originalTimeRange            % 原始时间范围
  .restoredTimeRange            % 还原时间范围
  .sourceFile                   % 源信号文件名
  .segmentNumber                % 段号
  .channel                      % 通道信息
```

## 时间轴还原

### 原理
原始分段文件的时间轴都从0开始，还原算法：
```
还原时间 = 原始时间 + (段号 - 1) × 60秒
```

### 示例
- seg001: [0,60]秒 → [0,60]秒
- seg002: [0,60]秒 → [60,120]秒  
- seg003: [0,60]秒 → [120,180]秒

## 故障排除

### 常见问题

1. **标注文件不存在**
   ```
   错误：标注文件不存在: 4、Label/ls_data3.mat
   解决：确保标注文件存在且路径正确
   ```

2. **原始数据文件不存在**
   ```
   警告：原始数据文件不存在: 1、Raw data/data3_seg002_tt.mat
   解决：检查原始数据文件是否完整
   ```

3. **信号名称解析失败**
   ```
   错误：无法解析信号名称: xxx
   解决：确保信号名称符合格式 data{N}_5min_seg{XXX}_tt{N}
   ```

### 调试步骤

1. 运行 `check_label_file.m` 检查标注文件结构
2. 运行 `test_multi_label_process.m` 进行全面测试
3. 检查MATLAB命令窗口的详细错误信息

## 系统要求

- MATLAB R2018b 或更高版本
- Signal Processing Toolbox
- 足够的磁盘空间（建议至少1GB）

## 性能提示

- 处理大量数据时，确保有足够的内存
- 可以分批处理以避免内存不足
- 定期清理输出文件夹以节省空间

## 技术支持

如遇问题，请检查：
1. 文件夹结构是否正确
2. 数据文件是否完整
3. MATLAB版本和工具箱是否满足要求
4. 查看处理报告中的详细信息
