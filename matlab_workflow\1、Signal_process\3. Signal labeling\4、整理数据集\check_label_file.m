%% 检查标注文件结构
% 用于分析 ls_data3.mat 文件的实际数据结构

clear; clc;

fprintf('=== 标注文件结构检查 ===\n\n');

labelFilePath = fullfile('4、Label', 'ls_data3.mat');

try
    fprintf('正在加载文件: %s\n', labelFilePath);
    
    % 检查文件是否存在
    if ~exist(labelFilePath, 'file')
        error('文件不存在: %s', labelFilePath);
    end
    
    % 加载文件
    data = load(labelFilePath);
    
    fprintf('✓ 文件加载成功\n\n');
    
    % 显示所有变量
    fprintf('文件中的变量:\n');
    fprintf('----------------------------------------\n');
    vars = fieldnames(data);
    for i = 1:length(vars)
        varName = vars{i};
        varValue = data.(varName);
        fprintf('%d. %s (%s)\n', i, varName, class(varValue));
        
        % 如果是结构体，显示字段
        if isstruct(varValue)
            fields = fieldnames(varValue);
            fprintf('   结构体字段: %s\n', strjoin(fields, ', '));
            
            % 检查是否有Labels字段
            if any(strcmp(fields, 'Labels'))
                fprintf('   ✓ 包含Labels字段\n');
                labels = varValue.Labels;
                fprintf('   Labels类型: %s\n', class(labels));
                if istable(labels)
                    fprintf('   Labels表格大小: %dx%d\n', height(labels), width(labels));
                    fprintf('   Labels列名: %s\n', strjoin(labels.Properties.VariableNames, ', '));
                end
            else
                fprintf('   ❌ 不包含Labels字段\n');
            end
        elseif istable(varValue)
            fprintf('   表格大小: %dx%d\n', height(varValue), width(varValue));
            fprintf('   列名: %s\n', strjoin(varValue.Properties.VariableNames, ', '));
        elseif iscell(varValue)
            fprintf('   单元格数组大小: %s\n', mat2str(size(varValue)));
        end
        fprintf('\n');
    end
    
    % 尝试不同的可能结构
    fprintf('=== 尝试识别标注数据结构 ===\n');
    
    % 检查是否直接有ls变量
    if isfield(data, 'ls')
        fprintf('✓ 找到ls变量\n');
        ls = data.ls;

        if isa(ls, 'labeledSignalSet')
            fprintf('✓ ls是labeledSignalSet对象\n');

            % 尝试访问Labels属性
            try
                labels = ls.Labels;
                fprintf('✓ 成功访问ls.Labels\n');
                fprintf('   Labels类型: %s\n', class(labels));

                if istable(labels)
                    fprintf('   Labels表格大小: %dx%d\n', height(labels), width(labels));
                    fprintf('   Labels列名: %s\n', strjoin(labels.Properties.VariableNames, ', '));

                    % 显示前几行数据
                    if height(labels) > 0
                        fprintf('   前几行数据:\n');
                        disp(labels(1:min(3, height(labels)), :));

                        % 检查Row字段
                        if any(strcmp(labels.Properties.VariableNames, 'Row'))
                            fprintf('   ✓ 包含Row列\n');
                            rows = labels.Row;
                            fprintf('   Row类型: %s\n', class(rows));
                            if iscell(rows) && ~isempty(rows)
                                fprintf('   第一个Row内容: %s\n', rows{1});
                            end
                        end
                    end
                end

            catch ME
                fprintf('❌ 无法访问ls.Labels: %s\n', ME.message);
            end

            % 显示labeledSignalSet的属性
            fprintf('   labeledSignalSet属性:\n');
            try
                fprintf('     NumMembers: %d\n', ls.NumMembers);
                fprintf('     SampleRate: %g\n', ls.SampleRate);
                fprintf('     Description: %s\n', ls.Description);
            catch
                fprintf('     无法访问某些属性\n');
            end

        elseif isstruct(ls)
            if isfield(ls, 'Labels')
                fprintf('✓ ls.Labels存在\n');
            else
                fprintf('❌ ls.Labels不存在，ls的字段: %s\n', strjoin(fieldnames(ls), ', '));
            end
        end
    else
        fprintf('❌ 没有找到ls变量\n');
        
        % 检查其他可能的变量名
        possibleNames = {'labeledSignalSet', 'lss', 'labels', 'data', 'result'};
        for i = 1:length(possibleNames)
            name = possibleNames{i};
            if isfield(data, name)
                fprintf('✓ 找到可能的标注变量: %s\n', name);
                var = data.(name);
                if isstruct(var) && isfield(var, 'Labels')
                    fprintf('  ✓ %s.Labels存在\n', name);
                elseif isa(var, 'labeledSignalSet')
                    fprintf('  ✓ %s是labeledSignalSet对象\n', name);
                end
            end
        end
    end
    
catch ME
    fprintf('❌ 检查过程出现错误: %s\n', ME.message);
    fprintf('错误位置: %s，第 %d 行\n', ME.stack(1).name, ME.stack(1).line);
end

fprintf('\n=== 检查完成 ===\n');
